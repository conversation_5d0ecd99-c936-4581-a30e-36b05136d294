# Go CF Proxy Configuration Example
# Copy this file to config.yaml and modify as needed

server:
  port: 8080
  host: "0.0.0.0"
  camouflage_host: "www.youku.com"

vless:
  uuid: "02a9780e-f549-41cb-b3de-a2e988a7170b"
  private_key_auth: false
  private_key: ""

proxy:
  enabled: true
  address: ""  # e.g., "ts.hpc.tw:443"

socks5:
  enabled: true
  global_enabled: false
  address: "bf9d9462-c27a-4f3a-9529-f531f627069b:bf9d9462-c27a-4f3a-9529-f531f627069b@**************:45678"

nodes:
  preferred_nodes:
    - "www.visa.com.sg:8443"
    - "www.visa.com.hk:8443"
    - "www.visa.co.jp"
    - "*************"
    - "**************"
  nodes_url: ""  # URL to fetch nodes from
  default_name: "天书9.0"

ui:
  subscription_path: "mycfbot2025"
  hide_subscription: false
  taunt_message: "哎呀你找到了我，但是我就是不给你看，气不气，嘿嘿嘿"
