# Go CF Proxy Makefile

# Variables
APP_NAME := go-cf-proxy
VERSION := 1.0.0
BUILD_DIR := build
BINARY := $(BUILD_DIR)/$(APP_NAME)
DOCKER_IMAGE := $(APP_NAME):$(VERSION)
DOCKER_IMAGE_LATEST := $(APP_NAME):latest

# Go parameters
GOCMD := go
GOBUILD := $(GOCMD) build
GOCLEAN := $(GOCMD) clean
GOTEST := $(GOCMD) test
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod

# Build flags
LDFLAGS := -ldflags "-X main.AppVersion=$(VERSION) -s -w"
BUILD_FLAGS := -a -installsuffix cgo

.PHONY: all build clean test deps docker docker-build docker-run help

# Default target
all: clean deps test build

# Build the application
build:
	@echo "Building $(APP_NAME) v$(VERSION)..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux $(GOBUILD) $(BUILD_FLAGS) $(LDFLAGS) -o $(BINARY) ./cmd/server
	@echo "Build complete: $(BINARY)"

# Build for multiple platforms
build-all:
	@echo "Building for multiple platforms..."
	@mkdir -p $(BUILD_DIR)
	
	# Linux AMD64
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(BUILD_FLAGS) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-linux-amd64 ./cmd/server
	
	# Linux ARM64
	CGO_ENABLED=0 GOOS=linux GOARCH=arm64 $(GOBUILD) $(BUILD_FLAGS) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-linux-arm64 ./cmd/server
	
	# Windows AMD64
	CGO_ENABLED=0 GOOS=windows GOARCH=amd64 $(GOBUILD) $(BUILD_FLAGS) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-windows-amd64.exe ./cmd/server
	
	# macOS AMD64
	CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 $(GOBUILD) $(BUILD_FLAGS) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-darwin-amd64 ./cmd/server
	
	# macOS ARM64
	CGO_ENABLED=0 GOOS=darwin GOARCH=arm64 $(GOBUILD) $(BUILD_FLAGS) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-darwin-arm64 ./cmd/server
	
	@echo "Multi-platform build complete"

# Clean build artifacts
clean:
	@echo "Cleaning..."
	@$(GOCLEAN)
	@rm -rf $(BUILD_DIR)
	@echo "Clean complete"

# Run tests
test:
	@echo "Running tests..."
	@$(GOTEST) -v ./...

# Run tests with coverage
test-coverage:
	@echo "Running tests with coverage..."
	@$(GOTEST) -v -coverprofile=coverage.out ./...
	@$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	@$(GOMOD) download
	@$(GOMOD) tidy

# Update dependencies
deps-update:
	@echo "Updating dependencies..."
	@$(GOGET) -u ./...
	@$(GOMOD) tidy

# Run the application
run:
	@echo "Running $(APP_NAME)..."
	@$(GOCMD) run ./cmd/server

# Run with config file
run-config:
	@echo "Running $(APP_NAME) with config..."
	@$(GOCMD) run ./cmd/server -config config.example.yaml

# Format code
fmt:
	@echo "Formatting code..."
	@$(GOCMD) fmt ./...

# Lint code
lint:
	@echo "Linting code..."
	@golangci-lint run

# Vet code
vet:
	@echo "Vetting code..."
	@$(GOCMD) vet ./...

# Security check
security:
	@echo "Running security check..."
	@gosec ./...

# Build Docker image
docker-build:
	@echo "Building Docker image..."
	@docker build -t $(DOCKER_IMAGE) .
	@docker tag $(DOCKER_IMAGE) $(DOCKER_IMAGE_LATEST)
	@echo "Docker image built: $(DOCKER_IMAGE)"

# Run Docker container
docker-run:
	@echo "Running Docker container..."
	@docker run -d -p 8080:8080 --name $(APP_NAME) $(DOCKER_IMAGE_LATEST)
	@echo "Container started: $(APP_NAME)"

# Stop Docker container
docker-stop:
	@echo "Stopping Docker container..."
	@docker stop $(APP_NAME) || true
	@docker rm $(APP_NAME) || true

# Docker Compose up
docker-compose-up:
	@echo "Starting with Docker Compose..."
	@docker-compose up -d

# Docker Compose down
docker-compose-down:
	@echo "Stopping Docker Compose..."
	@docker-compose down

# Install development tools
install-tools:
	@echo "Installing development tools..."
	@$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@$(GOGET) github.com/securecodewarrior/gosec/v2/cmd/gosec@latest

# Generate mocks (if using mockery)
generate-mocks:
	@echo "Generating mocks..."
	@mockery --all --output=mocks

# Create release
release: clean deps test build-all
	@echo "Creating release..."
	@mkdir -p $(BUILD_DIR)/release
	@cp $(BUILD_DIR)/$(APP_NAME)-* $(BUILD_DIR)/release/
	@cp config.example.yaml $(BUILD_DIR)/release/
	@cp README.md $(BUILD_DIR)/release/
	@cd $(BUILD_DIR)/release && tar -czf $(APP_NAME)-$(VERSION).tar.gz *
	@echo "Release created: $(BUILD_DIR)/release/$(APP_NAME)-$(VERSION).tar.gz"

# Show help
help:
	@echo "Available targets:"
	@echo "  build          - Build the application"
	@echo "  build-all      - Build for multiple platforms"
	@echo "  clean          - Clean build artifacts"
	@echo "  test           - Run tests"
	@echo "  test-coverage  - Run tests with coverage"
	@echo "  deps           - Download dependencies"
	@echo "  deps-update    - Update dependencies"
	@echo "  run            - Run the application"
	@echo "  run-config     - Run with config file"
	@echo "  fmt            - Format code"
	@echo "  lint           - Lint code"
	@echo "  vet            - Vet code"
	@echo "  security       - Run security check"
	@echo "  docker-build   - Build Docker image"
	@echo "  docker-run     - Run Docker container"
	@echo "  docker-stop    - Stop Docker container"
	@echo "  docker-compose-up   - Start with Docker Compose"
	@echo "  docker-compose-down - Stop Docker Compose"
	@echo "  install-tools  - Install development tools"
	@echo "  release        - Create release package"
	@echo "  help           - Show this help"
