package main

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/binary"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
	"sync"

	"golang.org/x/net/websocket"
)

// =================================================================
// Configuration Block - Mirrored from worker.js
// =================================================================

// Config holds all the configuration for the proxy.
type Config struct {
	SubscriptionPath   string
	VLESS_UUID         string
	PrivateKeyAuth     bool
	PrivateKey         string
	HideSubscription   bool
	TauntMessage       string
	PreferredNodes     []string
	PreferredNodesURL  string
	EnableProxy        bool
	ProxyAddress       string
	EnableSOCKS5       bool
	EnableSOCKS5Global bool
	SOCKS5Address      string // format: "user:pass@host:port"
	DefaultNodeName    string
	CamouflageHost     string
}

var config Config

func loadConfig() {
	config = Config{
		SubscriptionPath:   "mycfbot2025",
		VLESS_UUID:         "02a9780e-f549-41cb-b3de-a2e988a7170b",
		PrivateKeyAuth:     false,
		PrivateKey:         "",
		HideSubscription:   false,
		TauntMessage:       "哎呀你找到了我，但是我就是不给你看，气不气，嘿嘿嘿",
		PreferredNodes: []string{
			"www.visa.com.sg:8443",
			"www.visa.com.hk:8443",
			"www.visa.co.jp",
			"*************",
			"**************",
		},
		PreferredNodesURL:  "",
		EnableProxy:        true,
		ProxyAddress:       "", // e.g., "ts.hpc.tw:443"
		EnableSOCKS5:       true,
		EnableSOCKS5Global: false,
		SOCKS5Address:      "bf9d9462-c27a-4f3a-9529-f531f627069b:bf9d9462-c27a-4f3a-9529-f531f627069b@**************:45678",
		DefaultNodeName:    "天书9.0",
		CamouflageHost:     "www.youku.com",
	}
}

// =================================================================
// Main Entry Point
// =================================================================

func main() {
	loadConfig()

	http.HandleFunc("/", mainHandler)

	log.Println("Starting server on :8080")
	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatal("ListenAndServe: ", err)
	}
}

// =================================================================
// HTTP Request Handling
// =================================================================

func mainHandler(w http.ResponseWriter, r *http.Request) {
	// Handle WebSocket upgrade requests
	if strings.ToLower(r.Header.Get("Upgrade")) == "websocket" {
		handleWebSocket(w, r)
		return
	}

	// Handle regular HTTP requests
	// Fetch preferred nodes from URL if specified
	if config.PreferredNodesURL != "" {
		resp, err := http.Get(config.PreferredNodesURL)
		if err == nil {
			defer resp.Body.Close()
			body, err := io.ReadAll(resp.Body)
			if err == nil {
				lines := strings.Split(string(body), "
")
				var nodes []string
				for _, line := range lines {
					if trimmed := strings.TrimSpace(line); trimmed != "" {
						nodes = append(nodes, trimmed)
					}
				}
				if len(nodes) > 0 {
					config.PreferredNodes = nodes
				}
			}
		}
	}

	clashPath := fmt.Sprintf("/%s/clash", config.SubscriptionPath)
	vlessPath := fmt.Sprintf("/%s/vless", config.SubscriptionPath)

	switch r.URL.Path {
	case "/" + config.SubscriptionPath:
		w.Header().Set("Content-Type", "text/plain; charset=utf-8")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(generateSubscriptionInfoPage(r.Host)))

	case vlessPath:
		if config.HideSubscription {
			w.Header().Set("Content-Type", "text/plain; charset=utf-8")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(config.TauntMessage))
		} else {
			w.Header().Set("Content-Type", "text/plain; charset=utf-8")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(generateGenericConfig(r.Host)))
		}

	case clashPath:
		if config.HideSubscription {
			w.Header().Set("Content-Type", "text/plain; charset=utf-8")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(config.TauntMessage))
		} else {
			w.Header().Set("Content-Type", "text/plain; charset=utf-8")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(generateClashConfig(r.Host)))
		}

	default:
		// Camouflage by reverse proxying to another host
		if config.CamouflageHost != "" {
			target, err := url.Parse("https://" + config.CamouflageHost)
			if err != nil {
				http.Error(w, "Bad Gateway", http.StatusBadGateway)
				return
			}
			proxy := httputil.NewSingleHostReverseProxy(target)
			proxy.Director = func(req *http.Request) {
				req.URL.Scheme = target.Scheme
				req.URL.Host = target.Host
				req.Host = target.Host
			}
			proxy.ServeHTTP(w, r)
		} else {
			http.NotFound(w, r)
		}
	}
}

// =================================================================
// WebSocket and VLESS Proxy Logic
// =================================================================

func handleWebSocket(w http.ResponseWriter, r *http.Request) {
	if config.PrivateKeyAuth {
		if r.Header.Get("my-key") != config.PrivateKey {
			http.Error(w, "Forbidden", http.StatusForbidden)
			return
		}
	}

	// The Sec-Websocket-Protocol header contains the VLESS data
	vlessDataB64 := r.Header.Get("Sec-Websocket-Protocol")
	if vlessDataB64 == "" {
		http.Error(w, "Missing Sec-Websocket-Protocol header", http.StatusBadRequest)
		return
	}

	// Base64 decode (the JS uses a custom scheme, so we try RawURLEncoding first)
	vlessData, err := base64.RawURLEncoding.DecodeString(vlessDataB64)
	if err != nil {
		// Try standard encoding as a fallback
		vlessData, err = base64.StdEncoding.DecodeString(vlessDataB64)
		if err != nil {
			http.Error(w, "Invalid Base64 in Sec-Websocket-Protocol", http.StatusBadRequest)
			return
		}
	}

	// Create a websocket handler that will process the VLESS data
	wsHandler := websocket.Handler(func(ws *websocket.Conn) {
		defer ws.Close()
		processVLESS(ws, vlessData)
	})

	// Hijack the connection to use our custom handler
	wsHandler.ServeHTTP(w, r)
}

func processVLESS(ws *websocket.Conn, vlessData []byte) {
	// 1. Parse VLESS Header
	host, port, initialPayload, err := parseVmessHeader(vlessData)
	if err != nil {
		log.Printf("Header parsing error: %v", err)
		return
	}

	// 2. Establish backend connection
	var backendConn net.Conn
	var dialErr error

	dialer := func(ctx context.Context, network, addr string) (net.Conn, error) {
		return net.Dial(network, addr)
	}

	// Global SOCKS5 override
	if config.EnableProxy && config.EnableSOCKS5 && config.EnableSOCKS5Global {
		log.Printf("Global SOCKS5: connecting to %s:%d via SOCKS5", host, port)
		backendConn, dialErr = dialSOCKS5(config.SOCKS5Address, host, port)
	} else {
		// Attempt direct connection first
		targetAddr := net.JoinHostPort(host, fmt.Sprintf("%d", port))
		log.Printf("Attempting direct connection to %s", targetAddr)
		backendConn, dialErr = dialer(context.Background(), "tcp", targetAddr)

		// If direct fails, try failover options
		if dialErr != nil {
			log.Printf("Direct connection to %s failed: %v. Trying failover...", targetAddr, dialErr)
			if config.EnableProxy {
				if config.EnableSOCKS5 {
					log.Printf("Failover to SOCKS5: connecting to %s:%d", host, port)
					backendConn, dialErr = dialSOCKS5(config.SOCKS5Address, host, port)
				} else if config.ProxyAddress != "" {
					proxyHost, proxyPortStr, _ := net.SplitHostPort(config.ProxyAddress)
					if proxyPortStr == "" {
						proxyPortStr = fmt.Sprintf("%d", port) // Use original port if proxy port not specified
					}
					proxyAddr := net.JoinHostPort(proxyHost, proxyPortStr)
					log.Printf("Failover to proxy: %s", proxyAddr)
					backendConn, dialErr = dialer(context.Background(), "tcp", proxyAddr)
				}
			}
		}
	}

	if dialErr != nil {
		log.Printf("All connection attempts failed: %v", dialErr)
		return
	}
	defer backendConn.Close()
	log.Printf("Connection established to %s:%d", host, port)

	// 3. Start proxying
	// The JS code sends an initial 2-byte message `[0,0]`. This is likely a VLESS/VMess addon response.
	// Let's replicate that behavior.
	ws.Write([]byte{0, 0})

	// Write initial payload from header to backend
	if len(initialPayload) > 0 {
		if _, err := backendConn.Write(initialPayload); err != nil {
			log.Printf("Error writing initial payload to backend: %v", err)
			return
		}
	}

	// Pipe data between websocket and backend
	pipeConnections(ws, backendConn)
}

// pipeConnections handles the bidirectional data transfer.
func pipeConnections(client net.Conn, backend net.Conn) {
	var wg sync.WaitGroup
	wg.Add(2)

	go func() {
		defer wg.Done()
		defer backend.Close()
		defer client.Close()
		_, err := io.Copy(backend, client)
		if err != nil && !isClosedConnError(err) {
			log.Printf("Error copying from client to backend: %v", err)
		}
	}()

	go func() {
		defer wg.Done()
		defer client.Close()
		defer backend.Close()
		_, err := io.Copy(client, backend)
		if err != nil && !isClosedConnError(err) {
			log.Printf("Error copying from backend to client: %v", err)
		}
	}()

	wg.Wait()
	log.Println("Connections closed.")
}

// isClosedConnError checks if the error is a common network closed error.
func isClosedConnError(err error) bool {
	return strings.Contains(err.Error(), "use of closed network connection") ||
		strings.Contains(err.Error(), "broken pipe") ||
		err == io.EOF ||
		strings.Contains(err.Error(), "connection reset by peer")
}

// =================================================================
// VMess Protocol Parsing
// =================================================================

// parseVmessHeader parses a simplified VMess-like header.
// The JS implementation is non-standard; this is a reasonable interpretation.
// Format: [1:Ver][16:UUID][1:AddonLen][Addons][1:Cmd][2:Port][1:AddrType][N:Addr][Payload]
func parseVmessHeader(data []byte) (host string, port uint16, payload []byte, err error) {
	if len(data) < 18 {
		return "", 0, nil, fmt.Errorf("header too short for UUID: %d", len(data))
	}

	// Bytes 1-17: UUID Check
	if !config.PrivateKeyAuth {
		uuid := formatUUID(data[1:17])
		if uuid != config.VLESS_UUID {
			return "", 0, nil, fmt.Errorf("UUID mismatch, expected %s, got %s", config.VLESS_UUID, uuid)
		}
	}

	cursor := 17 // Start after UUID

	// Addons
	if len(data) < cursor+1 {
		return "", 0, nil, fmt.Errorf("header too short for addon length")
	}
	addonLen := int(data[cursor])
	cursor += 1 + addonLen

	// Command
	if len(data) < cursor+1 {
		return "", 0, nil, fmt.Errorf("header too short for command")
	}
	cursor += 1

	// Port
	if len(data) < cursor+2 {
		return "", 0, nil, fmt.Errorf("header too short for port")
	}
	port = binary.BigEndian.Uint16(data[cursor : cursor+2])
	cursor += 2

	// Address Type
	if len(data) < cursor+1 {
		return "", 0, nil, fmt.Errorf("header too short for address type")
	}
	addrType := data[cursor]
	cursor += 1

	// Address
	addrLen := 0
	switch addrType {
	case 1: // IPv4
		addrLen = 4
		if len(data) < cursor+addrLen {
			return "", 0, nil, fmt.Errorf("header too short for IPv4 address")
		}
		host = net.IP(data[cursor : cursor+addrLen]).String()
	case 2: // Domain
		if len(data) < cursor+1 {
			return "", 0, nil, fmt.Errorf("header too short for domain length")
		}
		addrLen = int(data[cursor])
		cursor += 1
		if len(data) < cursor+addrLen {
			return "", 0, nil, fmt.Errorf("header too short for domain")
		}
		host = string(data[cursor : cursor+addrLen])
	case 3: // IPv6
		addrLen = 16
		if len(data) < cursor+addrLen {
			return "", 0, nil, fmt.Errorf("header too short for IPv6 address")
		}
		host = net.IP(data[cursor : cursor+addrLen]).String()
	default:
		return "", 0, nil, fmt.Errorf("unsupported address type: %d", addrType)
	}
	cursor += addrLen

	payload = data[cursor:]
	return host, port, payload, nil
}

// formatUUID converts a 16-byte slice to a standard UUID string.
func formatUUID(b []byte) string {
	if len(b) != 16 {
		return ""
	}
	return fmt.Sprintf("%x-%x-%x-%x-%x", b[0:4], b[4:6], b[6:8], b[8:10], b[10:16])
}

// =================================================================
// SOCKS5 Client Logic
// =================================================================

type socks5Creds struct {
	user string
	pass string
	host string
	port string
}

func parseSOCKS5Address(addr string) (*socks5Creds, error) {
	parts := strings.Split(addr, "@")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid SOCKS5 address format: user:pass@host:port")
	}
	userPassParts := strings.Split(parts[0], ":")
	if len(userPassParts) != 2 {
		return nil, fmt.Errorf("invalid SOCKS5 user:pass format")
	}
	hostPortParts := strings.Split(parts[1], ":")
	if len(hostPortParts) != 2 {
		return nil, fmt.Errorf("invalid SOCKS5 host:port format")
	}
	return &socks5Creds{
		user: userPassParts[0],
		pass: userPassParts[1],
		host: hostPortParts[0],
		port: hostPortParts[1],
	}, nil
}

func dialSOCKS5(socksAddr, targetHost string, targetPort uint16) (net.Conn, error) {
	creds, err := parseSOCKS5Address(socksAddr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse SOCKS5 address: %w", err)
	}

	conn, err := net.Dial("tcp", net.JoinHostPort(creds.host, creds.port))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to SOCKS5 server: %w", err)
	}

	// 1. Send auth methods: Version 5, 2 methods: 0x00 (no auth), 0x02 (user/pass)
	if _, err := conn.Write([]byte{0x05, 0x02, 0x00, 0x02}); err != nil {
		conn.Close()
		return nil, fmt.Errorf("SOCKS5 auth method write failed: %w", err)
	}

	// 2. Read server auth choice
	resp := make([]byte, 2)
	if _, err := io.ReadFull(conn, resp); err != nil {
		conn.Close()
		return nil, fmt.Errorf("SOCKS5 auth method read failed: %w", err)
	}
	if resp[0] != 0x05 {
		conn.Close()
		return nil, fmt.Errorf("invalid SOCKS5 version from server: %d", resp[0])
	}

	// 3. Perform authentication if required
	if resp[1] == 0x02 { // User/pass auth
		buf := new(bytes.Buffer)
		buf.WriteByte(0x01) // Auth version
		buf.WriteByte(byte(len(creds.user)))
		buf.WriteString(creds.user)
		buf.WriteByte(byte(len(creds.pass)))
		buf.WriteString(creds.pass)

		if _, err := conn.Write(buf.Bytes()); err != nil {
			conn.Close()
			return nil, fmt.Errorf("SOCKS5 user/pass write failed: %w", err)
		}
		authResp := make([]byte, 2)
		if _, err := io.ReadFull(conn, authResp); err != nil {
			conn.Close()
			return nil, fmt.Errorf("SOCKS5 user/pass read failed: %w", err)
		}
		if authResp[0] != 0x01 || authResp[1] != 0x00 {
			conn.Close()
			return nil, fmt.Errorf("SOCKS5 authentication failed")
		}
	} else if resp[1] != 0x00 { // No auth required, but server chose something else
		conn.Close()
		return nil, fmt.Errorf("unsupported SOCKS5 auth method chosen by server: %d", resp[1])
	}

	// 4. Send connection request
	buf := new(bytes.Buffer)
	buf.Write([]byte{0x05, 0x01, 0x00}) // Ver, Cmd(CONNECT), Rsv

	ip := net.ParseIP(targetHost)
	if ip4 := ip.To4(); ip4 != nil {
		buf.WriteByte(0x01) // IPv4
		buf.Write(ip4)
	} else if ip6 := ip.To16(); ip6 != nil {
		buf.WriteByte(0x04) // IPv6
		buf.Write(ip6)
	} else {
		buf.WriteByte(0x03) // Domain name
		if len(targetHost) > 255 {
			return nil, fmt.Errorf("hostname too long for SOCKS5: %d > 255", len(targetHost))
		}
		buf.WriteByte(byte(len(targetHost)))
		buf.WriteString(targetHost)
	}
	binary.Write(buf, binary.BigEndian, targetPort)

	if _, err := conn.Write(buf.Bytes()); err != nil {
		conn.Close()
		return nil, fmt.Errorf("SOCKS5 connect request write failed: %w", err)
	}

	// 5. Read server response
	resp = make([]byte, 4) // Read up to Atyp
	if _, err := io.ReadFull(conn, resp); err != nil {
		conn.Close()
		return nil, fmt.Errorf("SOCKS5 connect response read failed: %w", err)
	}
	if resp[1] != 0x00 {
		conn.Close()
		return nil, fmt.Errorf("SOCKS5 connection failed with code: %d", resp[1])
	}

	// Drain the rest of the response (Bnd.Addr, Bnd.Port)
	addrLenToDrain := 0
	switch resp[3] {
	case 0x01:
		addrLenToDrain = 4 + 2 // IPv4 + port
	case 0x03:
		lenByte := make([]byte, 1)
		if _, err := io.ReadFull(conn, lenByte); err != nil {
			conn.Close()
			return nil, fmt.Errorf("SOCKS5 could not read domain len: %w", err)
		}
		addrLenToDrain = int(lenByte[0]) + 2 // domain + port
	case 0x04:
		addrLenToDrain = 16 + 2 // IPv6 + port
	}
	if addrLenToDrain > 0 {
		if _, err := io.CopyN(io.Discard, conn, int64(addrLenToDrain)); err != nil {
			conn.Close()
			return nil, fmt.Errorf("SOCKS5 failed to drain bind address: %w", err)
		}
	}

	return conn, nil
}

// =================================================================
// Subscription Generation
// =================================================================

func generateSubscriptionInfoPage(hostName string) string {
	return fmt.Sprintf(`
1、本worker的私钥功能只支持clash，仅openclash和clash meta测试过，其他clash类软件自行测试
2、若使用通用订阅请关闭私钥功能
3、其他需求自行研究
通用的：https://%s/%s/vless
猫咪的：https://%s/%s/clash
`, hostName, config.SubscriptionPath, hostName, config.SubscriptionPath)
}

func generateGenericConfig(hostName string) string {
	if config.PrivateKeyAuth {
		return "请先关闭私钥功能"
	}

	nodes := config.PreferredNodes
	if len(nodes) == 0 {
		nodes = []string{hostName + ":443"}
	}

	var result strings.Builder
	for i, node := range nodes {
		main, tlsOpt, _ := strings.Cut(node, "@")
		addrPart, namePart, _ := strings.Cut(main, "#")

		nodeName := config.DefaultNodeName
		if namePart != "" {
			nodeName = namePart
		}

		host, portStr, err := net.SplitHostPort(addrPart)
		if err != nil { // No port, assume 443
			host = addrPart
			portStr = "443"
		}

		tlsSetting := "security=tls"
		if tlsOpt == "notls" {
			tlsSetting = "security=none"
		}

		link := fmt.Sprintf("vless://%s@%s:%s?encryption=none&%s&sni=%s&type=ws&host=%s&path=%%2F%%3Fed%%3D2560#%s",
			config.VLESS_UUID,
			host,
			portStr,
			tlsSetting,
			hostName,
			hostName,
			url.QueryEscape(nodeName),
		)
		result.WriteString(link)
		if i < len(nodes)-1 {
			result.WriteString("
")
		}
	}
	return result.String()
}

func generateClashConfig(hostName string) string {
	nodes := config.PreferredNodes
	if len(nodes) == 0 {
		nodes = []string{hostName + ":443"}
	}

	var proxies strings.Builder
	var proxyNames strings.Builder

	for _, node := range nodes {
		main, tlsOpt, _ := strings.Cut(node, "@")
		addrPart, namePart, _ := strings.Cut(main, "#")

		nodeName := config.DefaultNodeName
		if namePart != "" {
			nodeName = namePart
		}

		host, portStr, err := net.SplitHostPort(addrPart)
		if err != nil {
			host = addrPart
			portStr = "443"
		}
		host = strings.Trim(host, "[]") // Remove brackets from IPv6 literal for Clash config

		tlsEnabled := "true"
		if tlsOpt == "notls" {
			tlsEnabled = "false"
		}

		fullNodeName := fmt.Sprintf("%s-%s-%s", nodeName, host, portStr)

		myKeyHeader := ""
		if config.PrivateKeyAuth && config.PrivateKey != "" {
			myKeyHeader = fmt.Sprintf("
        my-key: %s", config.PrivateKey)
		}

		proxies.WriteString(fmt.Sprintf(`
  - name: %s
    type: vless
    server: %s
    port: %s
    uuid: %s
    udp: false
    tls: %s
    sni: %s
    network: ws
    ws-opts:
      path: "/?ed=2560"
      headers:
        Host: %s%s`,
			fullNodeName, host, portStr, config.VLESS_UUID, tlsEnabled, hostName, hostName, myKeyHeader))

		proxyNames.WriteString(fmt.Sprintf("
    - %s", fullNodeName))
	}

	return fmt.Sprintf(`
dns:
  nameserver:
    - ************
    - 2400:da00::6666
  fallback:
    - *******
    - 2001:4860:4860::8888
proxies:%s
proxy-groups:
- name: 🚀 节点选择
  type: select
  proxies:
    - 自动选择%s
- name: 自动选择
  type: url-test
  url: http://www.gstatic.com/generate_204
  interval: 60
  tolerance: 30
  proxies:%s
- name: 漏网之鱼
  type: select
  proxies:
    - DIRECT
    - 🚀 节点选择
rules:
- GEOSITE,category-ads-all,REJECT
- GEOSITE,cn,DIRECT
- GEOIP,CN,DIRECT,no-resolve
- GEOSITE,cloudflare,🚀 节点选择
- GEOIP,CLOUDFLARE,🚀 节点选择,no-resolve
- GEOSITE,gfw,🚀 节点选择
- GEOSITE,google,🚀 节点选择
- GEOIP,GOOGLE,🚀 节点选择,no-resolve
- GEOSITE,netflix,🚀 节点选择
- GEOIP,NETFLIX,🚀 节点选择,no-resolve
- GEOSITE,telegram,🚀 节点选择
- GEOIP,TELEGRAM,🚀 节点选择,no-resolve
- GEOSITE,openai,🚀 节点选择
- MATCH,漏网之鱼
`, proxies.String(), proxyNames.String(), proxyNames.String())
}