# go-cf-proxy

A Go implementation of Cloudflare Worker VLESS proxy, rewritten from the original JavaScript version for better performance and maintainability.

## Features

- ✅ **VLESS Protocol Support**: Full VLESS protocol implementation with WebSocket transport
- ✅ **SOCKS5 Proxy**: Support for SOCKS5 proxy with authentication
- ✅ **Subscription Generation**: Generate VLESS and Clash subscription configurations
- ✅ **Private Key Authentication**: Optional private key authentication for enhanced security
- ✅ **Flexible Configuration**: Configure via environment variables, YAML file, or command line
- ✅ **Camouflage Mode**: Reverse proxy to another website for stealth
- ✅ **Health Monitoring**: Built-in health check endpoints
- ✅ **Docker Support**: Ready-to-use Docker containers
- ✅ **Graceful Shutdown**: Proper signal handling and graceful shutdown

## Quick Start

### Using Docker (Recommended)

1. **Clone the repository:**
   ```bash
   git clone https://github.com/your-repo/go-cf-proxy.git
   cd go-cf-proxy
   ```

2. **Run with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

3. **Access your proxy:**
   - Subscription info: `http://localhost:8080/mycfbot2025`
   - VLESS subscription: `http://localhost:8080/mycfbot2025/vless`
   - Clash subscription: `http://localhost:8080/mycfbot2025/clash`
   - Health check: `http://localhost:8080/health`

### Using Go Binary

1. **Build the application:**
   ```bash
   go build -o go-cf-proxy ./cmd/server
   ```

2. **Run with default configuration:**
   ```bash
   ./go-cf-proxy
   ```

3. **Run with custom configuration:**
   ```bash
   ./go-cf-proxy -config config.yaml
   ```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | `8080` |
| `HOST` | Server host | `0.0.0.0` |
| `VLESS_UUID` | VLESS UUID | `02a9780e-f549-41cb-b3de-a2e988a7170b` |
| `PRIVATE_KEY_AUTH` | Enable private key auth | `false` |
| `PRIVATE_KEY` | Private key for auth | `` |
| `PROXY_ENABLED` | Enable proxy functionality | `true` |
| `PROXYIP` | Proxy server address | `` |
| `SOCKS5OPEN` | Enable SOCKS5 proxy | `true` |
| `SOCKS5GLOBAL` | Enable global SOCKS5 | `false` |
| `SOCKS5` | SOCKS5 server (user:pass@host:port) | `` |
| `NODES_URL` | URL to fetch nodes | `` |
| `DEFAULT_NODE_NAME` | Default node name | `天书9.0` |
| `SUBSCRIPTION_PATH` | Subscription path | `mycfbot2025` |
| `HIDE_SUBSCRIPTION` | Hide subscription pages | `false` |
| `TAUNT_MESSAGE` | Message when hidden | `哎呀你找到了我...` |
| `CAMOUFLAGE_HOST` | Camouflage host | `www.youku.com` |

### Configuration File

Create a `config.yaml` file based on `config.example.yaml`:

```yaml
server:
  port: 8080
  host: "0.0.0.0"
  camouflage_host: "www.youku.com"

vless:
  uuid: "your-uuid-here"
  private_key_auth: false
  private_key: ""

proxy:
  enabled: true
  address: ""

socks5:
  enabled: true
  global_enabled: false
  address: "user:pass@host:port"

nodes:
  preferred_nodes:
    - "www.visa.com.sg:8443"
    - "www.visa.com.hk:8443"
  default_name: "天书9.0"

ui:
  subscription_path: "mycfbot2025"
  hide_subscription: false
```

## Usage Examples

### Basic Setup

```bash
# Set your UUID
export VLESS_UUID="your-uuid-here"

# Run the server
./go-cf-proxy
```

### With SOCKS5 Proxy

```bash
export SOCKS5OPEN=true
export SOCKS5="username:<EMAIL>:1080"
./go-cf-proxy
```

### With Private Key Authentication

```bash
export PRIVATE_KEY_AUTH=true
export PRIVATE_KEY="your-secret-key"
./go-cf-proxy
```

## API Endpoints

- `GET /{subscription_path}` - Subscription information page
- `GET /{subscription_path}/vless` - VLESS subscription
- `GET /{subscription_path}/clash` - Clash subscription
- `GET /health` - Health check
- `GET /config` - Configuration info (localhost only)
- `WebSocket /` - VLESS proxy endpoint

## Development

### Project Structure

```
go-cf-proxy/
├── cmd/server/          # Main application entry point
├── config/              # Configuration management
├── internal/
│   ├── handler/         # HTTP and WebSocket handlers
│   ├── protocol/        # VLESS protocol implementation
│   ├── proxy/           # SOCKS5 proxy client
│   └── subscription/    # Subscription generation
├── pkg/utils/           # Utility packages
├── config.example.yaml  # Example configuration
├── Dockerfile          # Docker build file
└── docker-compose.yml  # Docker Compose configuration
```

### Building from Source

```bash
# Clone repository
git clone https://github.com/your-repo/go-cf-proxy.git
cd go-cf-proxy

# Install dependencies
go mod download

# Build
go build -o go-cf-proxy ./cmd/server

# Run tests
go test ./...
```

### Docker Build

```bash
# Build image
docker build -t go-cf-proxy .

# Run container
docker run -p 8080:8080 -e VLESS_UUID=your-uuid go-cf-proxy
```

## Deployment

### Docker Compose

1. Copy `docker-compose.yml` to your server
2. Modify environment variables as needed
3. Run: `docker-compose up -d`

### Systemd Service

Create `/etc/systemd/system/go-cf-proxy.service`:

```ini
[Unit]
Description=Go CF Proxy
After=network.target

[Service]
Type=simple
User=nobody
ExecStart=/usr/local/bin/go-cf-proxy -config /etc/go-cf-proxy/config.yaml
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable go-cf-proxy
sudo systemctl start go-cf-proxy
```

## Security Considerations

1. **Change Default UUID**: Always use your own UUID
2. **Enable Private Key Auth**: For additional security in Clash clients
3. **Use HTTPS**: Deploy behind a reverse proxy with SSL/TLS
4. **Firewall**: Restrict access to necessary ports only
5. **Regular Updates**: Keep the application updated

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check if target servers are accessible
2. **UUID Mismatch**: Ensure client and server UUIDs match
3. **SOCKS5 Auth Failed**: Verify SOCKS5 credentials
4. **WebSocket Upgrade Failed**: Check if client supports WebSocket

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=debug
./go-cf-proxy
```

### Health Check

```bash
curl http://localhost:8080/health
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Original Cloudflare Worker implementation
- VLESS protocol specification
- Go WebSocket and HTTP libraries

## Support

- Create an issue for bug reports
- Discussions for questions and ideas
- Pull requests for contributions
