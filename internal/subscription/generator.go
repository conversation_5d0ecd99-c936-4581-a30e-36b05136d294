package subscription

import (
	"fmt"
	"net"
	"net/url"
	"strings"

	"go-cf-proxy/config"
)

// Generator handles subscription generation
type Generator struct {
	config *config.Config
}

// NewGenerator creates a new subscription generator
func NewGenerator(cfg *config.Config) *Generator {
	return &Generator{
		config: cfg,
	}
}

// GenerateInfoPage generates the subscription information page
func (g *Generator) GenerateInfoPage(hostName string) string {
	return fmt.Sprintf(`
1、本worker的私钥功能只支持clash，仅openclash和clash meta测试过，其他clash类软件自行测试
2、若使用通用订阅请关闭私钥功能
3、其他需求自行研究
通用的：https://%s/%s/vless
猫咪的：https://%s/%s/clash
`, hostName, g.config.UI.SubscriptionPath, hostName, g.config.UI.SubscriptionPath)
}

// GenerateVLESSConfig generates VLESS subscription configuration
func (g *Generator) GenerateVLESSConfig(hostName string) string {
	if g.config.VLESS.PrivateKeyAuth {
		return "请先关闭私钥功能"
	}

	nodes := g.getEffectiveNodes(hostName)
	var result strings.Builder

	for i, node := range nodes {
		link := g.generateVLESSLink(node, hostName)
		result.WriteString(link)
		if i < len(nodes)-1 {
			result.WriteString("\n")
		}
	}

	return result.String()
}

// GenerateClashConfig generates Clash configuration
func (g *Generator) GenerateClashConfig(hostName string) string {
	nodes := g.getEffectiveNodes(hostName)

	var proxies strings.Builder
	var proxyNames strings.Builder

	for _, node := range nodes {
		nodeConfig, proxyName := g.generateClashNode(node, hostName)
		proxies.WriteString(nodeConfig)
		proxyNames.WriteString(fmt.Sprintf("\n    - %s", proxyName))
	}

	return fmt.Sprintf(`
dns:
  nameserver:
    - ************
    - 2400:da00::6666
  fallback:
    - *******
    - 2001:4860:4860::8888
proxies:%s
proxy-groups:
- name: 🚀 节点选择
  type: select
  proxies:
    - 自动选择%s
- name: 自动选择
  type: url-test
  url: http://www.gstatic.com/generate_204
  interval: 60
  tolerance: 30
  proxies:%s
- name: 漏网之鱼
  type: select
  proxies:
    - DIRECT
    - 🚀 节点选择
rules:
- GEOSITE,category-ads-all,REJECT
- GEOSITE,cn,DIRECT
- GEOIP,CN,DIRECT,no-resolve
- GEOSITE,cloudflare,🚀 节点选择
- GEOIP,CLOUDFLARE,🚀 节点选择,no-resolve
- GEOSITE,gfw,🚀 节点选择
- GEOSITE,google,🚀 节点选择
- GEOIP,GOOGLE,🚀 节点选择,no-resolve
- GEOSITE,netflix,🚀 节点选择
- GEOIP,NETFLIX,🚀 节点选择,no-resolve
- GEOSITE,telegram,🚀 节点选择
- GEOIP,TELEGRAM,🚀 节点选择,no-resolve
- GEOSITE,openai,🚀 节点选择
- MATCH,漏网之鱼
`, proxies.String(), proxyNames.String(), proxyNames.String())
}

// getEffectiveNodes returns the effective list of nodes to use
func (g *Generator) getEffectiveNodes(hostName string) []string {
	nodes := g.config.Nodes.PreferredNodes
	if len(nodes) == 0 {
		nodes = []string{hostName + ":443"}
	}
	return nodes
}

// parseNodeConfig parses a node configuration string
func (g *Generator) parseNodeConfig(node string) (host, port, name string, tls bool) {
	// Split by @ to separate TLS option
	main, tlsOpt, _ := strings.Cut(node, "@")
	
	// Split by # to separate name
	addrPart, namePart, _ := strings.Cut(main, "#")
	
	// Set node name
	name = g.config.Nodes.DefaultName
	if namePart != "" {
		name = namePart
	}
	
	// Parse host and port
	host, port, err := net.SplitHostPort(addrPart)
	if err != nil {
		// No port specified, assume 443
		host = addrPart
		port = "443"
	}
	
	// Determine TLS setting
	tls = tlsOpt != "notls"
	
	return host, port, name, tls
}

// generateVLESSLink generates a VLESS subscription link
func (g *Generator) generateVLESSLink(node, hostName string) string {
	host, port, name, tls := g.parseNodeConfig(node)
	
	tlsSetting := "security=tls"
	if !tls {
		tlsSetting = "security=none"
	}
	
	link := fmt.Sprintf("vless://%s@%s:%s?encryption=none&%s&sni=%s&type=ws&host=%s&path=%%2F%%3Fed%%3D2560#%s",
		g.config.VLESS.UUID,
		host,
		port,
		tlsSetting,
		hostName,
		hostName,
		url.QueryEscape(name),
	)
	
	return link
}

// generateClashNode generates a Clash node configuration
func (g *Generator) generateClashNode(node, hostName string) (nodeConfig, proxyName string) {
	host, port, name, tls := g.parseNodeConfig(node)
	
	// Remove brackets from IPv6 addresses for Clash
	host = strings.Trim(host, "[]")
	
	tlsEnabled := "true"
	if !tls {
		tlsEnabled = "false"
	}
	
	proxyName = fmt.Sprintf("%s-%s-%s", name, host, port)
	
	myKeyHeader := ""
	if g.config.VLESS.PrivateKeyAuth && g.config.VLESS.PrivateKey != "" {
		myKeyHeader = fmt.Sprintf("\n        my-key: %s", g.config.VLESS.PrivateKey)
	}
	
	nodeConfig = fmt.Sprintf(`
  - name: %s
    type: vless
    server: %s
    port: %s
    uuid: %s
    udp: false
    tls: %s
    sni: %s
    network: ws
    ws-opts:
      path: "/?ed=2560"
      headers:
        Host: %s%s`,
		proxyName, host, port, g.config.VLESS.UUID, tlsEnabled, hostName, hostName, myKeyHeader)
	
	return nodeConfig, proxyName
}

// NodeInfo represents information about a node
type NodeInfo struct {
	Host string
	Port string
	Name string
	TLS  bool
}

// ParseNode parses a node configuration string into NodeInfo
func (g *Generator) ParseNode(node string) NodeInfo {
	host, port, name, tls := g.parseNodeConfig(node)
	return NodeInfo{
		Host: host,
		Port: port,
		Name: name,
		TLS:  tls,
	}
}

// ValidateNode validates a node configuration
func (g *Generator) ValidateNode(node string) error {
	info := g.ParseNode(node)
	
	if info.Host == "" {
		return fmt.Errorf("host cannot be empty")
	}
	
	if info.Port == "" {
		return fmt.Errorf("port cannot be empty")
	}
	
	// Validate port number
	if _, err := net.LookupPort("tcp", info.Port); err != nil {
		return fmt.Errorf("invalid port: %s", info.Port)
	}
	
	return nil
}
