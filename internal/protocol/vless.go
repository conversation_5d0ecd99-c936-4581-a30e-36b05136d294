package protocol

import (
	"encoding/binary"
	"fmt"
	"net"
	"strings"

	"github.com/google/uuid"
)

// AddressType represents the type of address in VLESS protocol
type AddressType byte

const (
	AddressTypeIPv4   AddressType = 1
	AddressTypeDomain AddressType = 2
	AddressTypeIPv6   AddressType = 3
)

// VLESSRequest represents a parsed VLESS request
type VLESSRequest struct {
	Version     byte
	UUID        string
	AddonLength byte
	Command     byte
	Port        uint16
	AddressType AddressType
	Address     string
	Payload     []byte
}

// ParseVLESSHeader parses a VLESS protocol header from raw data
func ParseVLESSHeader(data []byte, expectedUUID string, skipUUIDCheck bool) (*VLESSRequest, error) {
	if len(data) < 18 {
		return nil, fmt.Errorf("header too short for UUID: %d bytes", len(data))
	}

	req := &VLESSRequest{}

	// Parse version (byte 0)
	req.Version = data[0]

	// Parse UUID (bytes 1-16)
	uuidBytes := data[1:17]
	req.UUID = formatUUID(uuidBytes)

	// Validate UUID if required
	if !skipUUIDCheck && req.UUID != expectedUUID {
		return nil, fmt.Errorf("UUID mismatch: expected %s, got %s", expectedUUID, req.UUID)
	}

	cursor := 17

	// Parse addon length
	if len(data) < cursor+1 {
		return nil, fmt.Errorf("header too short for addon length")
	}
	req.AddonLength = data[cursor]
	cursor += 1 + int(req.AddonLength) // Skip addon data

	// Parse command
	if len(data) < cursor+1 {
		return nil, fmt.Errorf("header too short for command")
	}
	req.Command = data[cursor]
	cursor++

	// Parse port
	if len(data) < cursor+2 {
		return nil, fmt.Errorf("header too short for port")
	}
	req.Port = binary.BigEndian.Uint16(data[cursor : cursor+2])
	cursor += 2

	// Parse address type
	if len(data) < cursor+1 {
		return nil, fmt.Errorf("header too short for address type")
	}
	req.AddressType = AddressType(data[cursor])
	cursor++

	// Parse address based on type
	var addrLen int
	switch req.AddressType {
	case AddressTypeIPv4:
		addrLen = 4
		if len(data) < cursor+addrLen {
			return nil, fmt.Errorf("header too short for IPv4 address")
		}
		req.Address = net.IP(data[cursor : cursor+addrLen]).String()

	case AddressTypeDomain:
		if len(data) < cursor+1 {
			return nil, fmt.Errorf("header too short for domain length")
		}
		addrLen = int(data[cursor])
		cursor++
		if len(data) < cursor+addrLen {
			return nil, fmt.Errorf("header too short for domain")
		}
		req.Address = string(data[cursor : cursor+addrLen])

	case AddressTypeIPv6:
		addrLen = 16
		if len(data) < cursor+addrLen {
			return nil, fmt.Errorf("header too short for IPv6 address")
		}
		req.Address = net.IP(data[cursor : cursor+addrLen]).String()

	default:
		return nil, fmt.Errorf("unsupported address type: %d", req.AddressType)
	}

	cursor += addrLen

	// Extract remaining payload
	if cursor < len(data) {
		req.Payload = data[cursor:]
	}

	return req, nil
}

// formatUUID converts a 16-byte slice to a standard UUID string
func formatUUID(b []byte) string {
	if len(b) != 16 {
		return ""
	}
	return fmt.Sprintf("%x-%x-%x-%x-%x", b[0:4], b[4:6], b[6:8], b[8:10], b[10:16])
}

// ValidateUUID checks if a UUID string is valid
func ValidateUUID(uuidStr string) bool {
	_, err := uuid.Parse(uuidStr)
	return err == nil
}

// NormalizeUUID normalizes a UUID string to lowercase with dashes
func NormalizeUUID(uuidStr string) string {
	return strings.ToLower(uuidStr)
}

// GetTargetAddress returns the target address in host:port format
func (r *VLESSRequest) GetTargetAddress() string {
	return net.JoinHostPort(r.Address, fmt.Sprintf("%d", r.Port))
}

// String returns a string representation of the VLESS request
func (r *VLESSRequest) String() string {
	return fmt.Sprintf("VLESS{UUID: %s, Addr: %s, Port: %d, Type: %d}",
		r.UUID, r.Address, r.Port, r.AddressType)
}
