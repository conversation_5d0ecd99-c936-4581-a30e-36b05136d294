package proxy

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"io"
	"net"
	"strconv"
	"strings"
)

// SOCKS5Credentials holds SOCKS5 authentication credentials
type SOCKS5Credentials struct {
	Username string
	Password string
	Host     string
	Port     string
}

// ParseSOCKS5Address parses a SOCKS5 address string in format "user:pass@host:port"
func ParseSOCKS5Address(addr string) (*SOCKS5Credentials, error) {
	parts := strings.Split(addr, "@")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid SOCKS5 address format: expected user:pass@host:port")
	}

	userPassParts := strings.Split(parts[0], ":")
	if len(userPassParts) != 2 {
		return nil, fmt.<PERSON><PERSON><PERSON>("invalid SOCKS5 user:pass format")
	}

	hostPortParts := strings.Split(parts[1], ":")
	if len(hostPortParts) != 2 {
		return nil, fmt.<PERSON><PERSON><PERSON>("invalid SOCKS5 host:port format")
	}

	return &SOCKS5Credentials{
		Username: userPassParts[0],
		Password: userPassParts[1],
		Host:     hostPortParts[0],
		Port:     hostPortParts[1],
	}, nil
}

// DialSOCKS5 establishes a connection through a SOCKS5 proxy
func DialSOCKS5(socksAddr, targetHost string, targetPort uint16) (net.Conn, error) {
	creds, err := ParseSOCKS5Address(socksAddr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse SOCKS5 address: %w", err)
	}

	// Connect to SOCKS5 server
	conn, err := net.Dial("tcp", net.JoinHostPort(creds.Host, creds.Port))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to SOCKS5 server: %w", err)
	}

	// Perform SOCKS5 handshake
	if err := performSOCKS5Handshake(conn, creds, targetHost, targetPort); err != nil {
		conn.Close()
		return nil, fmt.Errorf("SOCKS5 handshake failed: %w", err)
	}

	return conn, nil
}

// performSOCKS5Handshake performs the complete SOCKS5 handshake
func performSOCKS5Handshake(conn net.Conn, creds *SOCKS5Credentials, targetHost string, targetPort uint16) error {
	// Step 1: Send authentication methods
	authMethods := []byte{0x05, 0x02, 0x00, 0x02} // Version 5, 2 methods: no auth (0x00), user/pass (0x02)
	if _, err := conn.Write(authMethods); err != nil {
		return fmt.Errorf("failed to send auth methods: %w", err)
	}

	// Step 2: Read server's chosen auth method
	resp := make([]byte, 2)
	if _, err := io.ReadFull(conn, resp); err != nil {
		return fmt.Errorf("failed to read auth method response: %w", err)
	}

	if resp[0] != 0x05 {
		return fmt.Errorf("invalid SOCKS5 version from server: %d", resp[0])
	}

	// Step 3: Perform authentication based on server's choice
	switch resp[1] {
	case 0x00: // No authentication required
		// Continue to connection request
	case 0x02: // Username/password authentication
		if err := performUserPassAuth(conn, creds); err != nil {
			return fmt.Errorf("user/pass authentication failed: %w", err)
		}
	case 0xFF: // No acceptable methods
		return fmt.Errorf("no acceptable authentication methods")
	default:
		return fmt.Errorf("unsupported authentication method: %d", resp[1])
	}

	// Step 4: Send connection request
	if err := sendConnectionRequest(conn, targetHost, targetPort); err != nil {
		return fmt.Errorf("connection request failed: %w", err)
	}

	return nil
}

// performUserPassAuth performs username/password authentication
func performUserPassAuth(conn net.Conn, creds *SOCKS5Credentials) error {
	buf := new(bytes.Buffer)
	buf.WriteByte(0x01) // Auth version
	buf.WriteByte(byte(len(creds.Username)))
	buf.WriteString(creds.Username)
	buf.WriteByte(byte(len(creds.Password)))
	buf.WriteString(creds.Password)

	if _, err := conn.Write(buf.Bytes()); err != nil {
		return fmt.Errorf("failed to send credentials: %w", err)
	}

	resp := make([]byte, 2)
	if _, err := io.ReadFull(conn, resp); err != nil {
		return fmt.Errorf("failed to read auth response: %w", err)
	}

	if resp[0] != 0x01 || resp[1] != 0x00 {
		return fmt.Errorf("authentication failed")
	}

	return nil
}

// sendConnectionRequest sends a SOCKS5 connection request
func sendConnectionRequest(conn net.Conn, targetHost string, targetPort uint16) error {
	buf := new(bytes.Buffer)
	buf.Write([]byte{0x05, 0x01, 0x00}) // Version, Command (CONNECT), Reserved

	// Determine address type and write address
	if ip := net.ParseIP(targetHost); ip != nil {
		if ip4 := ip.To4(); ip4 != nil {
			// IPv4
			buf.WriteByte(0x01)
			buf.Write(ip4)
		} else if ip6 := ip.To16(); ip6 != nil {
			// IPv6
			buf.WriteByte(0x04)
			buf.Write(ip6)
		}
	} else {
		// Domain name
		if len(targetHost) > 255 {
			return fmt.Errorf("hostname too long for SOCKS5: %d > 255", len(targetHost))
		}
		buf.WriteByte(0x03)
		buf.WriteByte(byte(len(targetHost)))
		buf.WriteString(targetHost)
	}

	// Write port
	binary.Write(buf, binary.BigEndian, targetPort)

	// Send request
	if _, err := conn.Write(buf.Bytes()); err != nil {
		return fmt.Errorf("failed to send connection request: %w", err)
	}

	// Read response
	resp := make([]byte, 4)
	if _, err := io.ReadFull(conn, resp); err != nil {
		return fmt.Errorf("failed to read connection response: %w", err)
	}

	if resp[0] != 0x05 {
		return fmt.Errorf("invalid SOCKS5 version in response: %d", resp[0])
	}

	if resp[1] != 0x00 {
		return fmt.Errorf("connection failed with code: %d", resp[1])
	}

	// Drain the bind address and port from response
	if err := drainBindAddress(conn, resp[3]); err != nil {
		return fmt.Errorf("failed to drain bind address: %w", err)
	}

	return nil
}

// drainBindAddress drains the bind address and port from the SOCKS5 response
func drainBindAddress(conn net.Conn, addrType byte) error {
	var bytesToDrain int

	switch addrType {
	case 0x01: // IPv4
		bytesToDrain = 4 + 2 // 4 bytes for IP + 2 bytes for port
	case 0x03: // Domain name
		lenByte := make([]byte, 1)
		if _, err := io.ReadFull(conn, lenByte); err != nil {
			return fmt.Errorf("failed to read domain length: %w", err)
		}
		bytesToDrain = int(lenByte[0]) + 2 // domain length + 2 bytes for port
	case 0x04: // IPv6
		bytesToDrain = 16 + 2 // 16 bytes for IP + 2 bytes for port
	default:
		return fmt.Errorf("unsupported address type in response: %d", addrType)
	}

	if bytesToDrain > 0 {
		if _, err := io.CopyN(io.Discard, conn, int64(bytesToDrain)); err != nil {
			return fmt.Errorf("failed to drain %d bytes: %w", bytesToDrain, err)
		}
	}

	return nil
}

// GetSOCKS5Port extracts the port from a SOCKS5 address string
func GetSOCKS5Port(socksAddr string) (int, error) {
	creds, err := ParseSOCKS5Address(socksAddr)
	if err != nil {
		return 0, err
	}
	return strconv.Atoi(creds.Port)
}

// GetSOCKS5Host extracts the host from a SOCKS5 address string
func GetSOCKS5Host(socksAddr string) (string, error) {
	creds, err := ParseSOCKS5Address(socksAddr)
	if err != nil {
		return "", err
	}
	return creds.Host, nil
}
