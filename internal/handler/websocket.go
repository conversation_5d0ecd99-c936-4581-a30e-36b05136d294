package handler

import (
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"

	"go-cf-proxy/config"
	"go-cf-proxy/internal/protocol"
	"go-cf-proxy/internal/proxy"
)

// WebSocketHandler handles WebSocket connections for VLESS proxy
type WebSocketHandler struct {
	config   *config.Config
	upgrader websocket.Upgrader
}

// NewWebSocketHandler creates a new WebSocket handler
func NewWebSocketHandler(cfg *config.Config) *WebSocketHandler {
	return &WebSocketHandler{
		config: cfg,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins for proxy usage
			},
			Subprotocols: []string{"binary"},
		},
	}
}

// HandleWebSocket handles WebSocket upgrade and VLESS proxy
func (h *WebSocketHandler) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
	// Check private key authentication if enabled
	if h.config.VLESS.PrivateKeyAuth {
		if r.Header.Get("my-key") != h.config.VLESS.PrivateKey {
			http.Error(w, "Forbidden", http.StatusForbidden)
			return
		}
	}

	// Get VLESS data from Sec-WebSocket-Protocol header
	vlessDataB64 := r.Header.Get("Sec-WebSocket-Protocol")
	if vlessDataB64 == "" {
		http.Error(w, "Missing Sec-WebSocket-Protocol header", http.StatusBadRequest)
		return
	}

	// Decode VLESS data
	vlessData, err := h.decodeVLESSData(vlessDataB64)
	if err != nil {
		log.Printf("Failed to decode VLESS data: %v", err)
		http.Error(w, "Invalid VLESS data", http.StatusBadRequest)
		return
	}

	// Upgrade to WebSocket
	conn, err := h.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}
	defer conn.Close()

	// Process VLESS proxy
	h.processVLESS(conn, vlessData)
}

// decodeVLESSData decodes base64-encoded VLESS data
func (h *WebSocketHandler) decodeVLESSData(data string) ([]byte, error) {
	// Try URL-safe base64 first (as used in the original JS)
	data = strings.ReplaceAll(data, "-", "+")
	data = strings.ReplaceAll(data, "_", "/")

	decoded, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		// Try raw URL encoding as fallback
		decoded, err = base64.RawURLEncoding.DecodeString(data)
		if err != nil {
			return nil, fmt.Errorf("failed to decode base64 data: %w", err)
		}
	}

	return decoded, nil
}

// processVLESS processes the VLESS proxy connection
func (h *WebSocketHandler) processVLESS(wsConn *websocket.Conn, vlessData []byte) {
	// Parse VLESS header
	req, err := protocol.ParseVLESSHeader(vlessData, h.config.VLESS.UUID, h.config.VLESS.PrivateKeyAuth)
	if err != nil {
		log.Printf("VLESS header parsing failed: %v", err)
		return
	}

	log.Printf("VLESS request: %s", req.String())

	// Establish backend connection
	backendConn, err := h.establishBackendConnection(req)
	if err != nil {
		log.Printf("Failed to establish backend connection: %v", err)
		return
	}
	defer backendConn.Close()

	log.Printf("Backend connection established to %s", req.GetTargetAddress())

	// Send initial WebSocket response (mimicking the original JS behavior)
	if err := wsConn.WriteMessage(websocket.BinaryMessage, []byte{0, 0}); err != nil {
		log.Printf("Failed to send initial WebSocket response: %v", err)
		return
	}

	// Write initial payload to backend if present
	if len(req.Payload) > 0 {
		if _, err := backendConn.Write(req.Payload); err != nil {
			log.Printf("Failed to write initial payload to backend: %v", err)
			return
		}
	}

	// Start bidirectional data transfer
	h.pipeConnections(wsConn, backendConn)
}

// establishBackendConnection establishes a connection to the target server
func (h *WebSocketHandler) establishBackendConnection(req *protocol.VLESSRequest) (net.Conn, error) {
	targetAddr := req.GetTargetAddress()

	// Global SOCKS5 override
	if h.config.Proxy.Enabled && h.config.SOCKS5.Enabled && h.config.SOCKS5.GlobalEnabled {
		log.Printf("Using global SOCKS5 proxy for %s", targetAddr)
		return proxy.DialSOCKS5(h.config.SOCKS5.Address, req.Address, req.Port)
	}

	// Try direct connection first
	dialer := &net.Dialer{
		Timeout: 30 * time.Second,
	}

	conn, err := dialer.DialContext(context.Background(), "tcp", targetAddr)
	if err == nil {
		return conn, nil
	}

	log.Printf("Direct connection to %s failed: %v, trying fallback options", targetAddr, err)

	// Try fallback options if direct connection fails
	if h.config.Proxy.Enabled {
		if h.config.SOCKS5.Enabled {
			log.Printf("Trying SOCKS5 proxy for %s", targetAddr)
			return proxy.DialSOCKS5(h.config.SOCKS5.Address, req.Address, req.Port)
		} else if h.config.Proxy.Address != "" {
			log.Printf("Trying HTTP proxy for %s", targetAddr)
			return h.dialThroughProxy(req)
		}
	}

	return nil, fmt.Errorf("all connection attempts failed for %s", targetAddr)
}

// dialThroughProxy dials through an HTTP proxy
func (h *WebSocketHandler) dialThroughProxy(req *protocol.VLESSRequest) (net.Conn, error) {
	proxyHost, proxyPort, err := net.SplitHostPort(h.config.Proxy.Address)
	if err != nil {
		// If no port specified, use the target port
		proxyHost = h.config.Proxy.Address
		proxyPort = fmt.Sprintf("%d", req.Port)
	}

	proxyAddr := net.JoinHostPort(proxyHost, proxyPort)
	dialer := &net.Dialer{
		Timeout: 30 * time.Second,
	}

	return dialer.DialContext(context.Background(), "tcp", proxyAddr)
}

// pipeConnections handles bidirectional data transfer between WebSocket and backend
func (h *WebSocketHandler) pipeConnections(wsConn *websocket.Conn, backendConn net.Conn) {
	var wg sync.WaitGroup
	wg.Add(2)

	// WebSocket to backend
	go func() {
		defer wg.Done()
		defer backendConn.Close()
		h.copyWSToBackend(wsConn, backendConn)
	}()

	// Backend to WebSocket
	go func() {
		defer wg.Done()
		defer wsConn.Close()
		h.copyBackendToWS(backendConn, wsConn)
	}()

	wg.Wait()
	log.Println("Connection closed")
}

// copyWSToBackend copies data from WebSocket to backend connection
func (h *WebSocketHandler) copyWSToBackend(wsConn *websocket.Conn, backendConn net.Conn) {
	for {
		messageType, data, err := wsConn.ReadMessage()
		if err != nil {
			if !isConnectionClosed(err) {
				log.Printf("Error reading from WebSocket: %v", err)
			}
			break
		}

		if messageType == websocket.BinaryMessage {
			if _, err := backendConn.Write(data); err != nil {
				if !isConnectionClosed(err) {
					log.Printf("Error writing to backend: %v", err)
				}
				break
			}
		}
	}
}

// copyBackendToWS copies data from backend connection to WebSocket
func (h *WebSocketHandler) copyBackendToWS(backendConn net.Conn, wsConn *websocket.Conn) {
	buffer := make([]byte, 32*1024) // 32KB buffer

	for {
		n, err := backendConn.Read(buffer)
		if err != nil {
			if err != io.EOF && !isConnectionClosed(err) {
				log.Printf("Error reading from backend: %v", err)
			}
			break
		}

		if n > 0 {
			if err := wsConn.WriteMessage(websocket.BinaryMessage, buffer[:n]); err != nil {
				if !isConnectionClosed(err) {
					log.Printf("Error writing to WebSocket: %v", err)
				}
				break
			}
		}
	}
}

// isConnectionClosed checks if the error indicates a closed connection
func isConnectionClosed(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	return strings.Contains(errStr, "use of closed network connection") ||
		strings.Contains(errStr, "broken pipe") ||
		strings.Contains(errStr, "connection reset by peer") ||
		strings.Contains(errStr, "websocket: close") ||
		err == io.EOF
}
