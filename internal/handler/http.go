package handler

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"

	"go-cf-proxy/config"
	"go-cf-proxy/internal/subscription"
)

// HTTPHandler handles HTTP requests
type HTTPHandler struct {
	config      *config.Config
	generator   *subscription.Generator
	wsHandler   *WebSocketHandler
}

// NewHTTPHandler creates a new HTTP handler
func NewHTTPHandler(cfg *config.Config) *HTTPHandler {
	return &HTTPHandler{
		config:    cfg,
		generator: subscription.NewGenerator(cfg),
		wsHandler: NewWebSocketHandler(cfg),
	}
}

// ServeHTTP implements the http.Handler interface
func (h *HTTPHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// Handle WebSocket upgrade requests
	if strings.ToLower(r.Header.Get("Upgrade")) == "websocket" {
		h.wsHandler.HandleWebSocket(w, r)
		return
	}

	// Update preferred nodes from URL if configured
	h.updateNodesFromURL()

	// Route HTTP requests
	h.routeHTTPRequest(w, r)
}

// updateNodesFromURL updates preferred nodes from external URL
func (h *HTTPHandler) updateNodesFromURL() {
	if h.config.Nodes.NodesURL == "" {
		return
	}

	resp, err := http.Get(h.config.Nodes.NodesURL)
	if err != nil {
		log.Printf("Failed to fetch nodes from URL %s: %v", h.config.Nodes.NodesURL, err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Failed to read nodes response: %v", err)
		return
	}

	lines := strings.Split(string(body), "\n")
	var nodes []string
	for _, line := range lines {
		if trimmed := strings.TrimSpace(line); trimmed != "" {
			nodes = append(nodes, trimmed)
		}
	}

	if len(nodes) > 0 {
		h.config.Nodes.PreferredNodes = nodes
		log.Printf("Updated %d nodes from external URL", len(nodes))
	}
}

// routeHTTPRequest routes HTTP requests to appropriate handlers
func (h *HTTPHandler) routeHTTPRequest(w http.ResponseWriter, r *http.Request) {
	path := r.URL.Path
	host := r.Host

	// Generate route paths
	infoPath := "/" + h.config.UI.SubscriptionPath
	vlessPath := fmt.Sprintf("/%s/vless", h.config.UI.SubscriptionPath)
	clashPath := fmt.Sprintf("/%s/clash", h.config.UI.SubscriptionPath)

	switch path {
	case infoPath:
		h.handleSubscriptionInfo(w, r, host)
	case vlessPath:
		h.handleVLESSSubscription(w, r, host)
	case clashPath:
		h.handleClashSubscription(w, r, host)
	default:
		h.handleCamouflage(w, r)
	}
}

// handleSubscriptionInfo handles subscription information page
func (h *HTTPHandler) handleSubscriptionInfo(w http.ResponseWriter, r *http.Request, host string) {
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	w.WriteHeader(http.StatusOK)
	
	content := h.generator.GenerateInfoPage(host)
	w.Write([]byte(content))
	
	log.Printf("Served subscription info page to %s", r.RemoteAddr)
}

// handleVLESSSubscription handles VLESS subscription generation
func (h *HTTPHandler) handleVLESSSubscription(w http.ResponseWriter, r *http.Request, host string) {
	if h.config.UI.HideSubscription {
		h.serveTauntMessage(w, r)
		return
	}

	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	w.WriteHeader(http.StatusOK)
	
	content := h.generator.GenerateVLESSConfig(host)
	w.Write([]byte(content))
	
	log.Printf("Served VLESS subscription to %s", r.RemoteAddr)
}

// handleClashSubscription handles Clash subscription generation
func (h *HTTPHandler) handleClashSubscription(w http.ResponseWriter, r *http.Request, host string) {
	if h.config.UI.HideSubscription {
		h.serveTauntMessage(w, r)
		return
	}

	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	w.WriteHeader(http.StatusOK)
	
	content := h.generator.GenerateClashConfig(host)
	w.Write([]byte(content))
	
	log.Printf("Served Clash subscription to %s", r.RemoteAddr)
}

// serveTauntMessage serves the taunt message when subscriptions are hidden
func (h *HTTPHandler) serveTauntMessage(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(h.config.UI.TauntMessage))
	
	log.Printf("Served taunt message to %s", r.RemoteAddr)
}

// handleCamouflage handles camouflage by reverse proxying to another host
func (h *HTTPHandler) handleCamouflage(w http.ResponseWriter, r *http.Request) {
	if h.config.Server.CamouflageHost == "" {
		http.NotFound(w, r)
		return
	}

	target, err := url.Parse("https://" + h.config.Server.CamouflageHost)
	if err != nil {
		log.Printf("Invalid camouflage host %s: %v", h.config.Server.CamouflageHost, err)
		http.Error(w, "Bad Gateway", http.StatusBadGateway)
		return
	}

	proxy := httputil.NewSingleHostReverseProxy(target)
	proxy.Director = func(req *http.Request) {
		req.URL.Scheme = target.Scheme
		req.URL.Host = target.Host
		req.Host = target.Host
		
		// Remove hop-by-hop headers
		req.Header.Del("Connection")
		req.Header.Del("Proxy-Connection")
		req.Header.Del("Te")
		req.Header.Del("Trailer")
		req.Header.Del("Transfer-Encoding")
		req.Header.Del("Upgrade")
	}

	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		log.Printf("Proxy error for %s: %v", r.URL.Path, err)
		http.Error(w, "Bad Gateway", http.StatusBadGateway)
	}

	log.Printf("Proxying request %s to %s", r.URL.Path, target.Host)
	proxy.ServeHTTP(w, r)
}

// HealthCheck provides a health check endpoint
func (h *HTTPHandler) HealthCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"status":"ok","service":"go-cf-proxy"}`))
}

// GetConfig returns the current configuration (for debugging)
func (h *HTTPHandler) GetConfig(w http.ResponseWriter, r *http.Request) {
	// Only allow from localhost for security
	if !strings.HasPrefix(r.RemoteAddr, "127.0.0.1") && !strings.HasPrefix(r.RemoteAddr, "[::1]") {
		http.Error(w, "Forbidden", http.StatusForbidden)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	
	// Return sanitized config (without sensitive data)
	sanitized := fmt.Sprintf(`{
		"server": {
			"port": %d,
			"host": "%s",
			"camouflage_host": "%s"
		},
		"vless": {
			"uuid": "%s",
			"private_key_auth": %t
		},
		"proxy": {
			"enabled": %t
		},
		"socks5": {
			"enabled": %t,
			"global_enabled": %t
		},
		"nodes": {
			"preferred_nodes": %d,
			"default_name": "%s"
		},
		"ui": {
			"subscription_path": "%s",
			"hide_subscription": %t
		}
	}`,
		h.config.Server.Port,
		h.config.Server.Host,
		h.config.Server.CamouflageHost,
		h.config.VLESS.UUID,
		h.config.VLESS.PrivateKeyAuth,
		h.config.Proxy.Enabled,
		h.config.SOCKS5.Enabled,
		h.config.SOCKS5.GlobalEnabled,
		len(h.config.Nodes.PreferredNodes),
		h.config.Nodes.DefaultName,
		h.config.UI.SubscriptionPath,
		h.config.UI.HideSubscription,
	)
	
	w.Write([]byte(sanitized))
}
