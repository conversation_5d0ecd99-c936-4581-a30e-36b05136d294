version: '3.8'

services:
  go-cf-proxy:
    build: .
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - HOST=0.0.0.0
      - VLESS_UUID=02a9780e-f549-41cb-b3de-a2e988a7170b
      - PRIVATE_KEY_AUTH=false
      - PRIVATE_KEY=
      - PROXY_ENABLED=true
      - PROXYIP=
      - SOCKS5OPEN=true
      - SOCKS5GLOBAL=false
      - SOCKS5=bf9d9462-c27a-4f3a-9529-f531f627069b:bf9d9462-c27a-4f3a-9529-f531f627069b@**************:45678
      - NODES_URL=
      - DEFAULT_NODE_NAME=天书9.0
      - SUBSCRIPTION_PATH=mycfbot2025
      - HIDE_SUBSCRIPTION=false
      - TAUNT_MESSAGE=哎呀你找到了我，但是我就是不给你看，气不气，嘿嘿嘿
      - CAMOUFLAGE_HOST=www.youku.com
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 5s
