package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"

	"gopkg.in/yaml.v3"
)

// Config holds all the configuration for the proxy server
type Config struct {
	Server ServerConfig `yaml:"server"`
	VLESS  VLESSConfig  `yaml:"vless"`
	Proxy  ProxyConfig  `yaml:"proxy"`
	SOCKS5 SOCKS5Config `yaml:"socks5"`
	Nodes  NodesConfig  `yaml:"nodes"`
	UI     UIConfig     `yaml:"ui"`
}

// ServerConfig contains server-related configuration
type ServerConfig struct {
	Port           int    `yaml:"port"`
	Host           string `yaml:"host"`
	CamouflageHost string `yaml:"camouflage_host"`
}

// VLESSConfig contains VLESS protocol configuration
type VLESSConfig struct {
	UUID           string `yaml:"uuid"`
	PrivateKeyAuth bool   `yaml:"private_key_auth"`
	PrivateKey     string `yaml:"private_key"`
}

// ProxyConfig contains proxy-related configuration
type ProxyConfig struct {
	Enabled bool   `yaml:"enabled"`
	Address string `yaml:"address"`
}

// SOCKS5Config contains SOCKS5 proxy configuration
type SOCKS5Config struct {
	Enabled       bool   `yaml:"enabled"`
	GlobalEnabled bool   `yaml:"global_enabled"`
	Address       string `yaml:"address"` // format: "user:pass@host:port"
}

// NodesConfig contains node configuration
type NodesConfig struct {
	PreferredNodes []string `yaml:"preferred_nodes"`
	NodesURL       string   `yaml:"nodes_url"`
	DefaultName    string   `yaml:"default_name"`
}

// UIConfig contains UI-related configuration
type UIConfig struct {
	SubscriptionPath string `yaml:"subscription_path"`
	HideSubscription bool   `yaml:"hide_subscription"`
	TauntMessage     string `yaml:"taunt_message"`
}

// DefaultConfig returns a configuration with default values
func DefaultConfig() *Config {
	return &Config{
		Server: ServerConfig{
			Port:           8080,
			Host:           "0.0.0.0",
			CamouflageHost: "www.youku.com",
		},
		VLESS: VLESSConfig{
			UUID:           "02a9780e-f549-41cb-b3de-a2e988a7170b",
			PrivateKeyAuth: false,
			PrivateKey:     "",
		},
		Proxy: ProxyConfig{
			Enabled: true,
			Address: "",
		},
		SOCKS5: SOCKS5Config{
			Enabled:       true,
			GlobalEnabled: false,
			Address:       "bf9d9462-c27a-4f3a-9529-f531f627069b:bf9d9462-c27a-4f3a-9529-f531f627069b@**************:45678",
		},
		Nodes: NodesConfig{
			PreferredNodes: []string{
				"www.visa.com.sg:8443",
				"www.visa.com.hk:8443",
				"www.visa.co.jp",
				"*************",
				"**************",
			},
			NodesURL:    "",
			DefaultName: "天书9.0",
		},
		UI: UIConfig{
			SubscriptionPath: "mycfbot2025",
			HideSubscription: false,
			TauntMessage:     "哎呀你找到了我，但是我就是不给你看，气不气，嘿嘿嘿",
		},
	}
}

// LoadConfig loads configuration from file and environment variables
func LoadConfig(configPath string) (*Config, error) {
	cfg := DefaultConfig()

	// Load from file if exists
	if configPath != "" {
		if err := loadFromFile(cfg, configPath); err != nil {
			return nil, fmt.Errorf("failed to load config from file: %w", err)
		}
	}

	// Override with environment variables
	loadFromEnv(cfg)

	return cfg, nil
}

// loadFromFile loads configuration from YAML file
func loadFromFile(cfg *Config, path string) error {
	data, err := os.ReadFile(path)
	if err != nil {
		return err
	}

	return yaml.Unmarshal(data, cfg)
}

// loadFromEnv loads configuration from environment variables
func loadFromEnv(cfg *Config) {
	// Server config
	if port := os.Getenv("PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			cfg.Server.Port = p
		}
	}
	if host := os.Getenv("HOST"); host != "" {
		cfg.Server.Host = host
	}
	if camouflage := os.Getenv("CAMOUFLAGE_HOST"); camouflage != "" {
		cfg.Server.CamouflageHost = camouflage
	}

	// VLESS config
	if uuid := os.Getenv("VLESS_UUID"); uuid != "" {
		cfg.VLESS.UUID = uuid
	}
	if privateKeyAuth := os.Getenv("PRIVATE_KEY_AUTH"); privateKeyAuth != "" {
		cfg.VLESS.PrivateKeyAuth = strings.ToLower(privateKeyAuth) == "true"
	}
	if privateKey := os.Getenv("PRIVATE_KEY"); privateKey != "" {
		cfg.VLESS.PrivateKey = privateKey
	}

	// Proxy config
	if proxyEnabled := os.Getenv("PROXY_ENABLED"); proxyEnabled != "" {
		cfg.Proxy.Enabled = strings.ToLower(proxyEnabled) == "true"
	}
	if proxyAddr := os.Getenv("PROXYIP"); proxyAddr != "" {
		cfg.Proxy.Address = proxyAddr
	}

	// SOCKS5 config
	if socks5Enabled := os.Getenv("SOCKS5OPEN"); socks5Enabled != "" {
		cfg.SOCKS5.Enabled = strings.ToLower(socks5Enabled) == "true"
	}
	if socks5Global := os.Getenv("SOCKS5GLOBAL"); socks5Global != "" {
		cfg.SOCKS5.GlobalEnabled = strings.ToLower(socks5Global) == "true"
	}
	if socks5Addr := os.Getenv("SOCKS5"); socks5Addr != "" {
		cfg.SOCKS5.Address = socks5Addr
	}

	// Nodes config
	if nodesURL := os.Getenv("NODES_URL"); nodesURL != "" {
		cfg.Nodes.NodesURL = nodesURL
	}
	if defaultName := os.Getenv("DEFAULT_NODE_NAME"); defaultName != "" {
		cfg.Nodes.DefaultName = defaultName
	}

	// UI config
	if subPath := os.Getenv("SUBSCRIPTION_PATH"); subPath != "" {
		cfg.UI.SubscriptionPath = subPath
	}
	if hideSub := os.Getenv("HIDE_SUBSCRIPTION"); hideSub != "" {
		cfg.UI.HideSubscription = strings.ToLower(hideSub) == "true"
	}
	if taunt := os.Getenv("TAUNT_MESSAGE"); taunt != "" {
		cfg.UI.TauntMessage = taunt
	}
}

// GetListenAddress returns the full listen address
func (c *Config) GetListenAddress() string {
	return fmt.Sprintf("%s:%d", c.Server.Host, c.Server.Port)
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.Server.Port <= 0 || c.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", c.Server.Port)
	}

	if c.VLESS.UUID == "" {
		return fmt.Errorf("VLESS UUID cannot be empty")
	}

	if c.VLESS.PrivateKeyAuth && c.VLESS.PrivateKey == "" {
		return fmt.Errorf("private key cannot be empty when private key auth is enabled")
	}

	if c.UI.SubscriptionPath == "" {
		return fmt.Errorf("subscription path cannot be empty")
	}

	return nil
}
