package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"go-cf-proxy/config"
	"go-cf-proxy/internal/handler"
	"go-cf-proxy/pkg/utils"
)

var (
	configPath = flag.String("config", "", "Path to configuration file")
	version    = flag.Bool("version", false, "Show version information")
	help       = flag.Bool("help", false, "Show help information")
)

const (
	AppName    = "go-cf-proxy"
	AppVersion = "1.0.0"
	AppDesc    = "Go implementation of Cloudflare Worker VLESS proxy"
)

func main() {
	flag.Parse()

	if *version {
		fmt.Printf("%s v%s\n%s\n", AppName, AppVersion, AppDesc)
		os.Exit(0)
	}

	if *help {
		showHelp()
		os.Exit(0)
	}

	// Setup logging
	utils.SetupLogging()

	log.Printf("Starting %s v%s", AppName, AppVersion)

	// Load configuration
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		log.Fatalf("Configuration validation failed: %v", err)
	}

	log.Printf("Configuration loaded successfully")
	log.Printf("Server will listen on %s", cfg.GetListenAddress())
	log.Printf("Subscription path: /%s", cfg.UI.SubscriptionPath)
	log.Printf("VLESS UUID: %s", cfg.VLESS.UUID)
	log.Printf("Private key auth: %t", cfg.VLESS.PrivateKeyAuth)
	log.Printf("Proxy enabled: %t", cfg.Proxy.Enabled)
	log.Printf("SOCKS5 enabled: %t", cfg.SOCKS5.Enabled)
	log.Printf("SOCKS5 global: %t", cfg.SOCKS5.GlobalEnabled)
	log.Printf("Preferred nodes: %d", len(cfg.Nodes.PreferredNodes))

	// Create HTTP handler
	httpHandler := handler.NewHTTPHandler(cfg)

	// Setup routes
	mux := http.NewServeMux()
	mux.Handle("/", httpHandler)
	mux.HandleFunc("/health", httpHandler.HealthCheck)
	mux.HandleFunc("/config", httpHandler.GetConfig)

	// Create server
	server := &http.Server{
		Addr:         cfg.GetListenAddress(),
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Server starting on %s", cfg.GetListenAddress())
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server failed to start: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Server is shutting down...")

	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Attempt graceful shutdown
	if err := server.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	} else {
		log.Println("Server shutdown completed")
	}
}

func showHelp() {
	fmt.Printf("%s v%s\n", AppName, AppVersion)
	fmt.Printf("%s\n\n", AppDesc)
	fmt.Println("Usage:")
	fmt.Printf("  %s [options]\n\n", os.Args[0])
	fmt.Println("Options:")
	fmt.Println("  -config string")
	fmt.Println("        Path to configuration file (optional)")
	fmt.Println("  -version")
	fmt.Println("        Show version information")
	fmt.Println("  -help")
	fmt.Println("        Show this help message")
	fmt.Println()
	fmt.Println("Environment Variables:")
	fmt.Println("  PORT                 Server port (default: 8080)")
	fmt.Println("  HOST                 Server host (default: 0.0.0.0)")
	fmt.Println("  VLESS_UUID           VLESS UUID")
	fmt.Println("  PRIVATE_KEY_AUTH     Enable private key authentication (true/false)")
	fmt.Println("  PRIVATE_KEY          Private key for authentication")
	fmt.Println("  PROXY_ENABLED        Enable proxy functionality (true/false)")
	fmt.Println("  PROXYIP              Proxy server address")
	fmt.Println("  SOCKS5OPEN           Enable SOCKS5 proxy (true/false)")
	fmt.Println("  SOCKS5GLOBAL         Enable global SOCKS5 proxy (true/false)")
	fmt.Println("  SOCKS5               SOCKS5 server address (user:pass@host:port)")
	fmt.Println("  NODES_URL            URL to fetch preferred nodes")
	fmt.Println("  DEFAULT_NODE_NAME    Default node name")
	fmt.Println("  SUBSCRIPTION_PATH    Subscription path")
	fmt.Println("  HIDE_SUBSCRIPTION    Hide subscription pages (true/false)")
	fmt.Println("  TAUNT_MESSAGE        Message to show when subscription is hidden")
	fmt.Println("  CAMOUFLAGE_HOST      Host to proxy unknown requests to")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Printf("  %s\n", os.Args[0])
	fmt.Printf("  %s -config config.yaml\n", os.Args[0])
	fmt.Printf("  PORT=8080 VLESS_UUID=your-uuid %s\n", os.Args[0])
	fmt.Println()
	fmt.Println("For more information, visit: https://github.com/your-repo/go-cf-proxy")
}
